#!/usr/bin/env python3
"""
AutoGen编程工作流运行脚本
提供命令行接口来运行不同的编程任务
"""

import asyncio
import argparse
import sys
from typing import Optional
from programming_workflow import ProgrammingWorkflow
from workflow_examples import AdvancedProgrammingWorkflow
from config import config, get_task_template, check_environment


async def run_simple_task(task_description: str, model_name: str = "gpt-4o"):
    """运行简单的编程任务"""
    print(f"🚀 使用模型 {model_name} 运行任务")
    print("=" * 60)
    
    workflow = ProgrammingWorkflow(model_name=model_name)
    
    try:
        result = await workflow.run_programming_task(task_description)
        print(f"\n✅ 任务完成，停止原因: {result.stop_reason}")
        return result
    finally:
        await workflow.close()


async def run_advanced_task(task_description: str, model_name: str = "gpt-4o", 
                          max_rounds: int = 15):
    """运行高级编程任务"""
    print(f"🚀 使用高级工作流运行任务 (模型: {model_name}, 最大轮次: {max_rounds})")
    print("=" * 60)
    
    workflow = AdvancedProgrammingWorkflow(
        model_name=model_name, 
        max_rounds=max_rounds
    )
    
    try:
        result = await workflow.run_programming_task(task_description)
        print(f"\n✅ 任务完成，停止原因: {result.stop_reason}")
        return result
    finally:
        await workflow.close()


async def run_template_task(template_name: str, template_args: dict, 
                          model_name: str = "gpt-4o"):
    """使用模板运行任务"""
    try:
        task_description = get_task_template(template_name, **template_args)
        print(f"📋 使用模板: {template_name}")
        return await run_advanced_task(task_description, model_name)
    except ValueError as e:
        print(f"❌ 模板错误: {e}")
        return None


def parse_template_args(args_str: str) -> dict:
    """解析模板参数字符串"""
    if not args_str:
        return {}
    
    args_dict = {}
    for pair in args_str.split(','):
        if '=' in pair:
            key, value = pair.split('=', 1)
            args_dict[key.strip()] = value.strip()
    
    return args_dict


async def interactive_mode():
    """交互式模式"""
    print("🤖 AutoGen编程工作流 - 交互式模式")
    print("输入 'help' 查看帮助，输入 'quit' 退出")
    print("=" * 60)
    
    workflow = AdvancedProgrammingWorkflow()
    
    try:
        while True:
            print("\n💭 请输入编程任务描述 (或命令):")
            user_input = input("> ").strip()
            
            if user_input.lower() in ['quit', 'exit', 'q']:
                print("👋 再见!")
                break
            elif user_input.lower() == 'help':
                print("""
📚 可用命令:
- help: 显示此帮助信息
- quit/exit/q: 退出程序
- reset: 重置工作流状态
- 直接输入任务描述开始编程任务

📝 任务示例:
- 实现一个二叉搜索树
- 创建一个Web API服务
- 编写排序算法
- 设计数据库模型
""")
                continue
            elif user_input.lower() == 'reset':
                await workflow.reset_workflow()
                continue
            elif not user_input:
                continue
            
            try:
                print(f"\n🎯 开始处理任务: {user_input}")
                result = await workflow.run_programming_task(user_input)
                print(f"\n✅ 任务完成: {result.stop_reason}")
            except Exception as e:
                print(f"❌ 任务执行失败: {e}")
    
    finally:
        await workflow.close()


def main():
    """主函数"""
    parser = argparse.ArgumentParser(
        description="AutoGen编程工作流",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
示例用法:
  # 运行简单任务
  python run_workflow.py --task "实现一个栈数据结构"
  
  # 使用高级工作流
  python run_workflow.py --task "实现LRU缓存" --advanced --max-rounds 20
  
  # 使用模板
  python run_workflow.py --template data_structure --template-args "data_structure_name=队列"
  
  # 交互式模式
  python run_workflow.py --interactive
        """
    )
    
    parser.add_argument(
        "--task", "-t",
        help="编程任务描述"
    )
    
    parser.add_argument(
        "--template",
        choices=["data_structure", "algorithm", "web_api", "data_processing"],
        help="使用预定义模板"
    )
    
    parser.add_argument(
        "--template-args",
        help="模板参数，格式: key1=value1,key2=value2"
    )
    
    parser.add_argument(
        "--model", "-m",
        default="gpt-4o",
        help="使用的模型名称 (默认: gpt-4o)"
    )
    
    parser.add_argument(
        "--advanced", "-a",
        action="store_true",
        help="使用高级工作流"
    )
    
    parser.add_argument(
        "--max-rounds",
        type=int,
        default=15,
        help="最大轮次 (默认: 15)"
    )
    
    parser.add_argument(
        "--interactive", "-i",
        action="store_true",
        help="启动交互式模式"
    )
    
    parser.add_argument(
        "--check-env",
        action="store_true",
        help="检查环境配置"
    )
    
    args = parser.parse_args()
    
    # 检查环境配置
    if args.check_env:
        if check_environment():
            print("✅ 环境配置正常")
            sys.exit(0)
        else:
            print("❌ 环境配置有问题")
            sys.exit(1)
    
    # 检查必要的环境变量
    if not check_environment():
        sys.exit(1)
    
    # 交互式模式
    if args.interactive:
        asyncio.run(interactive_mode())
        return
    
    # 模板模式
    if args.template:
        template_args = parse_template_args(args.template_args or "")
        asyncio.run(run_template_task(args.template, template_args, args.model))
        return
    
    # 任务模式
    if args.task:
        if args.advanced:
            asyncio.run(run_advanced_task(args.task, args.model, args.max_rounds))
        else:
            asyncio.run(run_simple_task(args.task, args.model))
        return
    
    # 如果没有指定任务，显示帮助
    parser.print_help()


if __name__ == "__main__":
    main()

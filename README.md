# AutoGen编程工作流

基于Microsoft AutoGen框架构建的智能编程工作流系统，实现了代码编写、审查和优化的三agent协作模式。

## 🌟 特性

- **三Agent协作**: 代码编写者、代码审查者、代码优化者
- **最新AutoGen API**: 使用AutoGen 0.4+ AgentChat框架
- **多模型支持**: OpenAI GPT-4o、GPT-3.5-turbo等
- **灵活配置**: 支持自定义agent行为和工作流参数
- **实时交互**: 支持流式输出和交互式模式
- **任务模板**: 预定义常见编程任务模板
- **完整示例**: 包含数据结构、算法、Web API等示例

## 🏗️ 架构设计

```
用户任务 → Agent1(编写代码) → Agent2(审查代码) → Agent3(优化代码) → 最终结果
```

### Agent职责分工

1. **代码编写者 (Coder Agent)**
   - 根据需求编写高质量代码
   - 遵循最佳实践和编程规范
   - 添加注释和文档字符串
   - 实现错误处理和边界条件

2. **代码审查者 (Reviewer Agent)**
   - 多维度代码审查(功能、性能、安全、可维护性)
   - 识别潜在问题和改进点
   - 提供具体的修改建议
   - 评估代码质量

3. **代码优化者 (Optimizer Agent)**
   - 基于审查意见优化代码
   - 提升性能和可读性
   - 完善错误处理和测试
   - 重构和模块化改进

## 🚀 快速开始

### 1. 安装依赖

```bash
pip install -r requirements.txt
```

### 2. 设置环境变量

```bash
export OPENAI_API_KEY="your-openai-api-key"
```

### 3. 运行示例

```python
import asyncio
from programming_workflow import ProgrammingWorkflow

async def main():
    workflow = ProgrammingWorkflow()
    
    task = """
    请实现一个Python栈数据结构，包含：
    1. push和pop操作
    2. peek操作查看栈顶
    3. is_empty检查是否为空
    4. 适当的错误处理
    """
    
    result = await workflow.run_programming_task(task)
    await workflow.close()

asyncio.run(main())
```

## 📋 使用方式

### 命令行接口

```bash
# 基本用法
python run_workflow.py --task "实现一个二叉搜索树"

# 使用高级工作流
python run_workflow.py --task "实现LRU缓存" --advanced --max-rounds 20

# 使用模板
python run_workflow.py --template data_structure --template-args "data_structure_name=队列"

# 交互式模式
python run_workflow.py --interactive

# 检查环境配置
python run_workflow.py --check-env
```

### 编程接口

```python
from programming_workflow import ProgrammingWorkflow
from workflow_examples import AdvancedProgrammingWorkflow

# 基础工作流
workflow = ProgrammingWorkflow(model_name="gpt-4o")

# 高级工作流
advanced_workflow = AdvancedProgrammingWorkflow(
    model_name="gpt-4o",
    max_rounds=15,
    enable_reflection=True
)
```

## 🎯 任务模板

系统提供了预定义的任务模板：

### 数据结构模板
```python
task = get_task_template("data_structure", 
                        data_structure_name="红黑树")
```

### 算法模板
```python
task = get_task_template("algorithm",
                        algorithm_name="A*路径规划",
                        problem_description="在网格地图中找最短路径")
```

### Web API模板
```python
task = get_task_template("web_api",
                        framework="FastAPI",
                        api_features="用户管理、身份验证、文件上传")
```

## 🔧 配置选项

### 模型配置
```python
from config import ModelConfig, ModelProvider

config = ModelConfig(
    provider=ModelProvider.OPENAI,
    model_name="gpt-4o",
    temperature=0.7,
    max_tokens=4000
)
```

### 工作流配置
```python
from config import WorkflowConfig, WorkflowMode

config = WorkflowConfig(
    mode=WorkflowMode.STANDARD,
    max_rounds=15,
    max_messages=50,
    enable_console_output=True
)
```

## 📚 示例集合

项目包含多个完整示例：

1. **数据结构实现**: LRU缓存、栈、队列、树结构
2. **算法实现**: 排序、搜索、图算法、动态规划
3. **Web API开发**: FastAPI、Flask、Django REST
4. **数据处理**: 数据清洗、分析、可视化

运行示例：
```bash
python workflow_examples.py
```

## 🛠️ 高级功能

### 自定义Agent
```python
from autogen_agentchat.agents import AssistantAgent

custom_agent = AssistantAgent(
    name="specialist",
    model_client=model_client,
    system_message="你是一个专门的领域专家...",
    reflect_on_tool_use=True
)
```

### 终止条件控制
```python
from autogen_agentchat.conditions import TextMentionTermination, MaxMessageTermination

# 文本触发终止
text_termination = TextMentionTermination("TASK_COMPLETE")

# 消息数量限制
max_msg_termination = MaxMessageTermination(max_messages=20)

# 组合条件
combined_termination = text_termination | max_msg_termination
```

### 流式输出
```python
async for message in workflow.team.run_stream(task="编程任务"):
    if isinstance(message, TaskResult):
        print(f"完成: {message.stop_reason}")
    else:
        print(f"{message.source}: {message.content}")
```

## 🔍 监控和调试

### 启用日志
```python
import logging
logging.basicConfig(level=logging.INFO)
```

### 性能监控
```python
result = await workflow.run_programming_task(task)
print(f"消息数量: {len(result.messages)}")
print(f"停止原因: {result.stop_reason}")
```

## 🤝 贡献指南

1. Fork项目
2. 创建功能分支
3. 提交更改
4. 发起Pull Request

## 📄 许可证

MIT License

## 🆘 故障排除

### 常见问题

1. **API密钥错误**
   ```bash
   export OPENAI_API_KEY="your-actual-api-key"
   ```

2. **依赖包版本冲突**
   ```bash
   pip install --upgrade autogen-agentchat autogen-ext
   ```

3. **网络连接问题**
   - 检查网络连接
   - 配置代理设置
   - 使用Azure OpenAI替代

### 调试模式
```python
workflow = ProgrammingWorkflow()
workflow.team.enable_debug = True
```

## 📞 支持

- 📧 邮箱: [支持邮箱]
- 💬 讨论: [GitHub Discussions]
- 🐛 问题: [GitHub Issues]

---

**注意**: 本项目基于AutoGen 0.4+版本开发，请确保使用最新版本的AutoGen框架。

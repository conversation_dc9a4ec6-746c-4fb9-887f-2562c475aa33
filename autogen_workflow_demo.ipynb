{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["# AutoGen编程工作流演示\n", "\n", "本notebook演示如何使用AutoGen框架构建编程工作流，实现代码编写、审查和优化的三agent协作。"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 1. 环境设置和导入"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# 安装必要的包（如果还没有安装）\n", "# !pip install -U \"autogen-agentchat\" \"autogen-ext[openai]\"\n", "\n", "import os\n", "import asyncio\n", "from programming_workflow import ProgrammingWorkflow\n", "from workflow_examples import AdvancedProgrammingWorkflow\n", "from config import get_task_template, check_environment\n", "\n", "# 设置API密钥（请替换为您的实际API密钥）\n", "# os.environ[\"OPENAI_API_KEY\"] = \"your-openai-api-key-here\"\n", "\n", "# 检查环境配置\n", "if check_environment():\n", "    print(\"✅ 环境配置正常\")\n", "else:\n", "    print(\"❌ 请设置OPENAI_API_KEY环境变量\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 2. 基础工作流示例"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# 创建基础工作流\n", "workflow = ProgrammingWorkflow(model_name=\"gpt-4o\")\n", "\n", "# 定义一个简单的编程任务\n", "simple_task = \"\"\"\n", "请实现一个Python函数来计算斐波那契数列的第n项。\n", "要求：\n", "1. 使用递归和迭代两种方法\n", "2. 包含输入验证\n", "3. 添加性能比较\n", "4. 包含使用示例\n", "\"\"\"\n", "\n", "print(\"🚀 开始执行基础工作流...\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# 运行工作流\n", "result = await workflow.run_programming_task(simple_task)\n", "\n", "print(f\"\\n✅ 任务完成！\")\n", "print(f\"停止原因: {result.stop_reason}\")\n", "print(f\"总消息数: {len(result.messages)}\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 3. 高级工作流示例"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# 重置工作流\n", "await workflow.reset_workflow()\n", "\n", "# 创建高级工作流\n", "advanced_workflow = AdvancedProgrammingWorkflow(\n", "    model_name=\"gpt-4o\",\n", "    max_rounds=20,\n", "    enable_reflection=True\n", ")\n", "\n", "# 定义一个复杂的编程任务\n", "complex_task = \"\"\"\n", "请实现一个线程安全的LRU缓存类，要求：\n", "\n", "功能需求：\n", "1. get(key) - 获取缓存值，O(1)时间复杂度\n", "2. put(key, value) - 设置缓存值，O(1)时间复杂度\n", "3. 当缓存满时，移除最久未使用的项目\n", "4. 支持设置缓存容量\n", "\n", "技术要求：\n", "1. 线程安全实现\n", "2. 使用双向链表+哈希表\n", "3. 包含完整的类型提示\n", "4. 实现统计功能(命中率、缓存大小等)\n", "5. 包含完整的单元测试\n", "6. 添加性能基准测试\n", "\"\"\"\n", "\n", "print(\"🚀 开始执行高级工作流...\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# 运行高级工作流\n", "advanced_result = await advanced_workflow.run_programming_task(complex_task)\n", "\n", "print(f\"\\n✅ 高级任务完成！\")\n", "print(f\"停止原因: {advanced_result.stop_reason}\")\n", "print(f\"总消息数: {len(advanced_result.messages)}\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 4. 使用任务模板"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# 使用数据结构模板\n", "data_structure_task = get_task_template(\n", "    \"data_structure\",\n", "    data_structure_name=\"红黑树\"\n", ")\n", "\n", "print(\"📋 数据结构任务模板:\")\n", "print(data_structure_task)\n", "\n", "# 使用算法模板\n", "algorithm_task = get_task_template(\n", "    \"algorithm\",\n", "    algorithm_name=\"A*路径规划算法\",\n", "    problem_description=\"在二维网格地图中找到从起点到终点的最短路径，考虑障碍物和地形权重\"\n", ")\n", "\n", "print(\"\\n🧮 算法任务模板:\")\n", "print(algorithm_task)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 5. 流式输出演示"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# 重置工作流\n", "await advanced_workflow.reset_workflow()\n", "\n", "# 定义一个中等复杂度的任务\n", "streaming_task = \"\"\"\n", "请实现一个简单的二叉搜索树(BST)类，包含：\n", "1. 插入节点\n", "2. 删除节点\n", "3. 查找节点\n", "4. 中序遍历\n", "5. 获取树的高度\n", "\"\"\"\n", "\n", "print(\"🌊 开始流式输出演示...\")\n", "print(\"=\" * 50)\n", "\n", "# 使用流式输出\n", "async for message in advanced_workflow.team.run_stream(task=streaming_task):\n", "    if hasattr(message, 'source') and hasattr(message, 'content'):\n", "        print(f\"\\n[{message.source}]: {message.content[:100]}...\")\n", "    <PERSON><PERSON> has<PERSON>r(message, 'stop_reason'):\n", "        print(f\"\\n✅ 完成: {message.stop_reason}\")\n", "        break"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 6. 工作流状态管理"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# 检查工作流状态\n", "print(\"📊 工作流状态信息:\")\n", "print(f\"Agent数量: {len(advanced_workflow.team.participants)}\")\n", "print(f\"最大轮次: {advanced_workflow.max_rounds}\")\n", "print(f\"启用反思: {advanced_workflow.enable_reflection}\")\n", "\n", "# 获取agent信息\n", "for i, agent in enumerate(advanced_workflow.team.participants):\n", "    print(f\"Agent {i+1}: {agent.name}\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 7. 自定义终止条件"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["from autogen_agentchat.conditions import TextMentionTermination, MaxMessageTermination\n", "from autogen_agentchat.teams import RoundRobinGroupChat\n", "\n", "# 创建自定义终止条件\n", "custom_termination = TextMentionTermination(\"TASK_FINISHED\")\n", "max_msg_termination = MaxMessageTermination(max_messages=10)\n", "\n", "# 组合终止条件\n", "combined_termination = custom_termination | max_msg_termination\n", "\n", "# 创建带有自定义终止条件的团队\n", "custom_team = RoundRobinGroupChat(\n", "    participants=[\n", "        advanced_workflow.coder_agent,\n", "        advanced_workflow.reviewer_agent,\n", "        advanced_workflow.optimizer_agent\n", "    ],\n", "    termination_condition=combined_termination\n", ")\n", "\n", "print(\"🎛️ 自定义终止条件已设置\")\n", "print(\"- 文本触发: 'TASK_FINISHED'\")\n", "print(\"- 最大消息数: 10\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 8. 清理资源"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# 关闭工作流连接\n", "await workflow.close()\n", "await advanced_workflow.close()\n", "\n", "print(\"🔒 所有工作流连接已关闭\")\n", "print(\"✅ 演示完成！\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 总结\n", "\n", "本notebook演示了AutoGen编程工作流的主要功能：\n", "\n", "1. **基础工作流**: 简单的三agent协作模式\n", "2. **高级工作流**: 支持更多配置和功能\n", "3. **任务模板**: 预定义的常见编程任务\n", "4. **流式输出**: 实时查看agent交互过程\n", "5. **状态管理**: 工作流状态监控和控制\n", "6. **自定义配置**: 灵活的终止条件和参数设置\n", "\n", "### 下一步\n", "\n", "- 尝试不同类型的编程任务\n", "- 自定义agent的系统消息\n", "- 实验不同的模型和参数\n", "- 集成到您的开发工作流中\n", "\n", "### 相关资源\n", "\n", "- [AutoGen官方文档](https://microsoft.github.io/autogen/)\n", "- [项目GitHub仓库](https://github.com/microsoft/autogen)\n", "- [更多示例和教程](https://microsoft.github.io/autogen/stable/user-guide/agentchat-user-guide/examples/)"]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.10.0"}}, "nbformat": 4, "nbformat_minor": 4}
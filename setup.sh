#!/bin/bash

# AutoGen编程工作流安装脚本

echo "🚀 AutoGen编程工作流安装脚本"
echo "================================"

# 检查Python版本
python_version=$(python3 --version 2>&1 | grep -oP '\d+\.\d+' | head -1)
required_version="3.10"

if [ "$(printf '%s\n' "$required_version" "$python_version" | sort -V | head -n1)" != "$required_version" ]; then
    echo "❌ 错误: 需要Python 3.10或更高版本，当前版本: $python_version"
    exit 1
fi

echo "✅ Python版本检查通过: $python_version"

# 创建虚拟环境
echo "📦 创建虚拟环境..."
python3 -m venv autogen_env

# 激活虚拟环境
echo "🔄 激活虚拟环境..."
source autogen_env/bin/activate

# 升级pip
echo "⬆️ 升级pip..."
pip install --upgrade pip

# 安装依赖
echo "📥 安装依赖包..."
pip install -r requirements.txt

# 检查安装
echo "🔍 检查安装..."
python -c "import autogen_agentchat; print('✅ AutoGen AgentChat安装成功')"
python -c "import autogen_ext; print('✅ AutoGen Extensions安装成功')"

# 检查环境变量
echo "🔧 检查环境配置..."
if [ -z "$OPENAI_API_KEY" ]; then
    echo "⚠️ 警告: 未设置OPENAI_API_KEY环境变量"
    echo "请运行: export OPENAI_API_KEY='your-api-key'"
else
    echo "✅ OPENAI_API_KEY已设置"
fi

# 运行测试
echo "🧪 运行基础测试..."
python test_workflow.py --unit-only

echo ""
echo "🎉 安装完成！"
echo ""
echo "📋 使用说明:"
echo "1. 激活虚拟环境: source autogen_env/bin/activate"
echo "2. 设置API密钥: export OPENAI_API_KEY='your-api-key'"
echo "3. 运行示例: python run_workflow.py --interactive"
echo "4. 查看帮助: python run_workflow.py --help"
echo ""
echo "📚 更多信息请查看README.md"

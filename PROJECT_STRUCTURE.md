# AutoGen编程工作流项目结构

本项目实现了基于Microsoft AutoGen框架的编程工作流，包含代码编写、审查和优化的三agent协作模式。

## 📁 项目文件结构

```
ai-test/
├── README.md                      # 项目说明文档
├── PROJECT_STRUCTURE.md           # 项目结构说明（本文件）
├── requirements.txt               # Python依赖包列表
├── setup.sh                      # Linux/macOS安装脚本
├── setup.bat                     # Windows安装脚本
│
├── programming_workflow.py       # 核心工作流实现
├── workflow_examples.py          # 高级工作流和示例
├── config.py                     # 配置管理
├── run_workflow.py               # 命令行运行脚本
├── test_workflow.py              # 测试脚本
└── autogen_workflow_demo.ipynb   # Jupyter演示notebook
```

## 🔧 核心文件说明

### 1. programming_workflow.py
**主要工作流实现**
- `ProgrammingWorkflow` 类：基础三agent工作流
- Agent配置：代码编写者、审查者、优化者
- 终止条件设置
- 基本运行和管理方法

### 2. workflow_examples.py
**高级工作流和示例**
- `AdvancedProgrammingWorkflow` 类：增强版工作流
- 多个完整的编程任务示例
- 不同复杂度的任务演示
- 性能优化和配置选项

### 3. config.py
**配置管理系统**
- 模型配置（OpenAI、Azure等）
- Agent配置（系统消息、行为参数）
- 工作流配置（模式、限制、输出格式）
- 任务模板定义
- 环境检查功能

### 4. run_workflow.py
**命令行接口**
- 支持多种运行模式
- 任务模板支持
- 交互式模式
- 参数配置选项

### 5. test_workflow.py
**测试套件**
- 单元测试
- 集成测试
- 模拟测试
- 环境验证

## 🎯 使用场景

### 1. 数据结构实现
```bash
python run_workflow.py --template data_structure --template-args "data_structure_name=红黑树"
```

### 2. 算法开发
```bash
python run_workflow.py --template algorithm --template-args "algorithm_name=A*搜索,problem_description=路径规划"
```

### 3. Web API开发
```bash
python run_workflow.py --template web_api --template-args "framework=FastAPI,api_features=用户管理"
```

### 4. 自定义任务
```bash
python run_workflow.py --task "实现一个分布式缓存系统" --advanced --max-rounds 20
```

## 🔄 工作流程

```mermaid
graph TD
    A[用户输入任务] --> B[代码编写者Agent]
    B --> C[代码审查者Agent]
    C --> D{审查通过?}
    D -->|是| E[任务完成]
    D -->|否| F[代码优化者Agent]
    F --> G[优化代码]
    G --> C
    C --> H{达到最大轮次?}
    H -->|是| E
    H -->|否| D
```

## 🛠️ Agent角色定义

### 1. 代码编写者 (Coder Agent)
- **职责**: 根据需求编写初始代码
- **技能**: 
  - 多语言编程能力
  - 最佳实践应用
  - 文档编写
  - 错误处理

### 2. 代码审查者 (Reviewer Agent)
- **职责**: 全面审查代码质量
- **评估维度**:
  - 功能正确性
  - 性能效率
  - 安全性
  - 可维护性
  - 代码规范

### 3. 代码优化者 (Optimizer Agent)
- **职责**: 基于审查意见优化代码
- **优化方向**:
  - 性能提升
  - 代码重构
  - 安全加固
  - 测试完善

## 📊 配置选项

### 模型配置
```python
ModelConfig(
    provider=ModelProvider.OPENAI,
    model_name="gpt-4o",
    temperature=0.7,
    max_tokens=4000
)
```

### 工作流配置
```python
WorkflowConfig(
    mode=WorkflowMode.STANDARD,
    max_rounds=15,
    max_messages=50,
    enable_console_output=True
)
```

### Agent配置
```python
AgentConfig(
    name="coder",
    system_message="专业程序员系统消息",
    enable_reflection=True,
    max_consecutive_auto_reply=5
)
```

## 🚀 快速开始

### 1. 安装
```bash
# Linux/macOS
./setup.sh

# Windows
setup.bat
```

### 2. 配置
```bash
export OPENAI_API_KEY="your-api-key"
```

### 3. 运行
```bash
# 交互式模式
python run_workflow.py --interactive

# 直接任务
python run_workflow.py --task "实现快速排序算法"

# 使用模板
python run_workflow.py --template algorithm --template-args "algorithm_name=快速排序"
```

## 🧪 测试

### 运行所有测试
```bash
python test_workflow.py
```

### 只运行单元测试
```bash
python test_workflow.py --unit-only
```

### 只运行集成测试
```bash
python test_workflow.py --integration-only
```

## 📈 扩展性

### 1. 添加新的Agent
```python
custom_agent = AssistantAgent(
    name="specialist",
    model_client=model_client,
    system_message="专业领域专家消息"
)
```

### 2. 自定义终止条件
```python
custom_termination = TextMentionTermination("CUSTOM_COMPLETE")
```

### 3. 新增任务模板
```python
TASK_TEMPLATES["new_template"] = """
新的任务模板内容...
{parameter1}
{parameter2}
"""
```

## 🔍 监控和调试

### 启用详细日志
```python
import logging
logging.basicConfig(level=logging.DEBUG)
```

### 性能监控
```python
result = await workflow.run_programming_task(task)
print(f"消息数: {len(result.messages)}")
print(f"停止原因: {result.stop_reason}")
```

### 流式输出调试
```python
async for message in workflow.team.run_stream(task):
    print(f"[{message.source}]: {message.content}")
```

## 📝 最佳实践

1. **任务描述要清晰具体**
2. **合理设置最大轮次限制**
3. **根据任务复杂度选择合适的模型**
4. **使用模板提高效率**
5. **定期重置工作流状态**
6. **监控API使用量和成本**

## 🤝 贡献指南

1. Fork项目
2. 创建功能分支
3. 编写测试
4. 提交代码
5. 发起Pull Request

## 📞 支持

- 📧 技术支持: [邮箱地址]
- 💬 社区讨论: [讨论链接]
- 🐛 问题报告: [Issues链接]
- 📚 文档: [文档链接]

---

**注意**: 本项目基于AutoGen 0.4+版本，请确保使用兼容的依赖版本。

"""
AutoGen编程工作流配置文件
包含各种配置选项和环境设置
"""

import os
from typing import Dict, Any, Optional
from dataclasses import dataclass
from enum import Enum


class ModelProvider(Enum):
    """支持的模型提供商"""
    OPENAI = "openai"
    AZURE_OPENAI = "azure_openai"
    ANTHROPIC = "anthropic"
    GOOGLE = "google"


class WorkflowMode(Enum):
    """工作流模式"""
    STANDARD = "standard"  # 标准模式：编写->审查->优化
    ITERATIVE = "iterative"  # 迭代模式：多轮优化
    PARALLEL = "parallel"  # 并行模式：多个方案同时开发
    COLLABORATIVE = "collaborative"  # 协作模式：多个专家同时参与


@dataclass
class ModelConfig:
    """模型配置"""
    provider: ModelProvider
    model_name: str
    api_key: Optional[str] = None
    api_base: Optional[str] = None
    api_version: Optional[str] = None
    temperature: float = 0.7
    max_tokens: Optional[int] = None
    timeout: int = 60


@dataclass
class AgentConfig:
    """Agent配置"""
    name: str
    system_message: str
    enable_reflection: bool = True
    enable_streaming: bool = True
    max_consecutive_auto_reply: int = 10


@dataclass
class WorkflowConfig:
    """工作流配置"""
    mode: WorkflowMode = WorkflowMode.STANDARD
    max_rounds: int = 15
    max_messages: int = 50
    enable_console_output: bool = True
    enable_logging: bool = True
    log_level: str = "INFO"
    output_format: str = "markdown"


class Config:
    """主配置类"""
    
    def __init__(self):
        self.model_configs = self._load_model_configs()
        self.agent_configs = self._load_agent_configs()
        self.workflow_config = self._load_workflow_config()
    
    def _load_model_configs(self) -> Dict[str, ModelConfig]:
        """加载模型配置"""
        return {
            "gpt-4o": ModelConfig(
                provider=ModelProvider.OPENAI,
                model_name="gpt-4o",
                api_key=os.getenv("OPENAI_API_KEY"),
                temperature=0.7,
                max_tokens=4000,
            ),
            "gpt-4o-mini": ModelConfig(
                provider=ModelProvider.OPENAI,
                model_name="gpt-4o-mini",
                api_key=os.getenv("OPENAI_API_KEY"),
                temperature=0.7,
                max_tokens=4000,
            ),
            "gpt-3.5-turbo": ModelConfig(
                provider=ModelProvider.OPENAI,
                model_name="gpt-3.5-turbo",
                api_key=os.getenv("OPENAI_API_KEY"),
                temperature=0.7,
                max_tokens=4000,
            ),
            "azure-gpt-4": ModelConfig(
                provider=ModelProvider.AZURE_OPENAI,
                model_name="gpt-4",
                api_key=os.getenv("AZURE_OPENAI_API_KEY"),
                api_base=os.getenv("AZURE_OPENAI_ENDPOINT"),
                api_version=os.getenv("AZURE_OPENAI_API_VERSION", "2024-02-15-preview"),
                temperature=0.7,
            ),
        }
    
    def _load_agent_configs(self) -> Dict[str, AgentConfig]:
        """加载Agent配置"""
        return {
            "coder": AgentConfig(
                name="coder",
                system_message="""你是一个专业的程序员，负责编写高质量的代码。
请遵循最佳实践，包含适当的注释和错误处理。""",
                enable_reflection=True,
                max_consecutive_auto_reply=5,
            ),
            "reviewer": AgentConfig(
                name="reviewer",
                system_message="""你是一个代码审查专家，负责审查代码质量。
请从功能、性能、安全、可维护性等角度进行全面审查。""",
                enable_reflection=True,
                max_consecutive_auto_reply=3,
            ),
            "optimizer": AgentConfig(
                name="optimizer",
                system_message="""你是一个代码优化专家，负责优化和重构代码。
请基于审查意见进行针对性优化，提升代码质量。""",
                enable_reflection=True,
                max_consecutive_auto_reply=5,
            ),
        }
    
    def _load_workflow_config(self) -> WorkflowConfig:
        """加载工作流配置"""
        return WorkflowConfig(
            mode=WorkflowMode(os.getenv("WORKFLOW_MODE", "standard")),
            max_rounds=int(os.getenv("MAX_ROUNDS", "15")),
            max_messages=int(os.getenv("MAX_MESSAGES", "50")),
            enable_console_output=os.getenv("ENABLE_CONSOLE", "true").lower() == "true",
            enable_logging=os.getenv("ENABLE_LOGGING", "true").lower() == "true",
            log_level=os.getenv("LOG_LEVEL", "INFO"),
            output_format=os.getenv("OUTPUT_FORMAT", "markdown"),
        )
    
    def get_model_config(self, model_name: str) -> ModelConfig:
        """获取模型配置"""
        if model_name not in self.model_configs:
            raise ValueError(f"未找到模型配置: {model_name}")
        return self.model_configs[model_name]
    
    def get_agent_config(self, agent_name: str) -> AgentConfig:
        """获取Agent配置"""
        if agent_name not in self.agent_configs:
            raise ValueError(f"未找到Agent配置: {agent_name}")
        return self.agent_configs[agent_name]


# 全局配置实例
config = Config()


# 预定义的任务模板
TASK_TEMPLATES = {
    "data_structure": """
请实现一个{data_structure_name}数据结构，要求：
1. 实现基本操作方法
2. 包含完整的类型提示
3. 添加适当的错误处理
4. 包含使用示例和测试用例
5. 优化时间和空间复杂度
""",
    
    "algorithm": """
请实现{algorithm_name}算法，解决以下问题：
{problem_description}

要求：
1. 算法实现正确且高效
2. 包含详细的注释说明
3. 分析时间和空间复杂度
4. 处理边界条件和异常情况
5. 提供测试用例验证正确性
""",
    
    "web_api": """
请使用{framework}框架实现一个Web API，功能包括：
{api_features}

技术要求：
1. RESTful API设计
2. 数据验证和错误处理
3. 身份验证和授权
4. API文档生成
5. 单元测试和集成测试
""",
    
    "data_processing": """
请实现一个数据处理程序，处理{data_type}数据：
{processing_requirements}

要求：
1. 高效的数据处理算法
2. 支持大数据量处理
3. 错误处理和数据验证
4. 可配置的处理参数
5. 性能监控和日志记录
""",
}


def get_task_template(template_name: str, **kwargs) -> str:
    """获取任务模板并填充参数"""
    if template_name not in TASK_TEMPLATES:
        raise ValueError(f"未找到任务模板: {template_name}")
    
    template = TASK_TEMPLATES[template_name]
    try:
        return template.format(**kwargs)
    except KeyError as e:
        raise ValueError(f"模板参数缺失: {e}")


# 环境变量检查
def check_environment():
    """检查环境变量配置"""
    required_vars = ["OPENAI_API_KEY"]
    missing_vars = []
    
    for var in required_vars:
        if not os.getenv(var):
            missing_vars.append(var)
    
    if missing_vars:
        print(f"⚠️ 缺少必要的环境变量: {', '.join(missing_vars)}")
        print("请设置这些环境变量后再运行程序")
        return False
    
    return True


if __name__ == "__main__":
    # 测试配置
    print("🔧 配置测试")
    print(f"模型配置数量: {len(config.model_configs)}")
    print(f"Agent配置数量: {len(config.agent_configs)}")
    print(f"工作流模式: {config.workflow_config.mode.value}")
    
    # 检查环境
    if check_environment():
        print("✅ 环境配置正常")
    else:
        print("❌ 环境配置有问题")

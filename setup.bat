@echo off
REM AutoGen编程工作流安装脚本 (Windows版本)

echo 🚀 AutoGen编程工作流安装脚本
echo ================================

REM 检查Python版本
python --version >nul 2>&1
if errorlevel 1 (
    echo ❌ 错误: 未找到Python，请先安装Python 3.10或更高版本
    pause
    exit /b 1
)

echo ✅ Python检查通过

REM 创建虚拟环境
echo 📦 创建虚拟环境...
python -m venv autogen_env

REM 激活虚拟环境
echo 🔄 激活虚拟环境...
call autogen_env\Scripts\activate.bat

REM 升级pip
echo ⬆️ 升级pip...
python -m pip install --upgrade pip

REM 安装依赖
echo 📥 安装依赖包...
pip install -r requirements.txt

REM 检查安装
echo 🔍 检查安装...
python -c "import autogen_agentchat; print('✅ AutoGen AgentChat安装成功')"
python -c "import autogen_ext; print('✅ AutoGen Extensions安装成功')"

REM 检查环境变量
echo 🔧 检查环境配置...
if "%OPENAI_API_KEY%"=="" (
    echo ⚠️ 警告: 未设置OPENAI_API_KEY环境变量
    echo 请运行: set OPENAI_API_KEY=your-api-key
) else (
    echo ✅ OPENAI_API_KEY已设置
)

REM 运行测试
echo 🧪 运行基础测试...
python test_workflow.py --unit-only

echo.
echo 🎉 安装完成！
echo.
echo 📋 使用说明:
echo 1. 激活虚拟环境: autogen_env\Scripts\activate.bat
echo 2. 设置API密钥: set OPENAI_API_KEY=your-api-key
echo 3. 运行示例: python run_workflow.py --interactive
echo 4. 查看帮助: python run_workflow.py --help
echo.
echo 📚 更多信息请查看README.md

pause

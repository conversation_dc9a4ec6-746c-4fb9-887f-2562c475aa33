"""
AutoGen编程工作流
实现三个agent的协作：代码编写、代码审查、代码优化

使用最新的AutoGen AgentChat API
"""

import asyncio
from typing import List, Optional
from autogen_agentchat.agents import AssistantAgent
from autogen_agentchat.teams import RoundRobinGroupChat
from autogen_agentchat.conditions import TextMentionTermination, MaxMessageTermination
from autogen_agentchat.ui import Console
from autogen_ext.models.openai import OpenAIChatCompletionClient


class ProgrammingWorkflow:
    """编程工作流类，管理三个agent的协作"""
    
    def __init__(self, model_name: str = "gpt-4o", api_key: Optional[str] = None):
        """
        初始化工作流
        
        Args:
            model_name: 使用的模型名称
            api_key: OpenAI API密钥，如果为None则从环境变量获取
        """
        # 创建模型客户端
        self.model_client = OpenAIChatCompletionClient(
            model=model_name,
            api_key=api_key,  # 如果为None，会自动从OPENAI_API_KEY环境变量获取
        )
        
        # 初始化三个agent
        self._setup_agents()
        
        # 设置终止条件
        self._setup_termination_conditions()
        
        # 创建团队
        self._setup_team()
    
    def _setup_agents(self):
        """设置三个agent"""
        
        # Agent 1: 代码编写者
        self.coder_agent = AssistantAgent(
            name="coder",
            model_client=self.model_client,
            system_message="""你是一个专业的Python程序员。你的职责是：
1. 根据用户需求编写高质量的Python代码
2. 确保代码具有良好的可读性和结构
3. 添加适当的注释和文档字符串
4. 遵循Python编程最佳实践
5. 在代码完成后，说明代码的功能和使用方法

请始终提供完整、可运行的代码解决方案。""",
            reflect_on_tool_use=True,
        )
        
        # Agent 2: 代码审查者
        self.reviewer_agent = AssistantAgent(
            name="reviewer",
            model_client=self.model_client,
            system_message="""你是一个经验丰富的代码审查专家。你的职责是：
1. 仔细审查提供的代码
2. 检查代码的正确性、效率和安全性
3. 识别潜在的bug、性能问题或安全漏洞
4. 评估代码的可读性和可维护性
5. 提出具体的改进建议
6. 检查是否遵循了Python编程规范（PEP 8等）

请提供详细的审查报告，包括：
- 代码优点
- 发现的问题
- 具体的改进建议
- 如果代码质量满足要求，请在最后说"REVIEW_APPROVED"
""",
        )
        
        # Agent 3: 代码优化者
        self.optimizer_agent = AssistantAgent(
            name="optimizer",
            model_client=self.model_client,
            system_message="""你是一个代码优化专家。你的职责是：
1. 基于原始代码和审查意见，重新优化代码
2. 修复审查中发现的问题
3. 提升代码的性能、可读性和可维护性
4. 确保优化后的代码功能完整且正确
5. 添加更好的错误处理和边界条件检查
6. 优化算法复杂度和内存使用

请提供优化后的完整代码，并说明：
- 进行了哪些优化
- 解决了哪些问题
- 性能或质量的提升
- 在优化完成后，请说"OPTIMIZATION_COMPLETE"
""",
        )
    
    def _setup_termination_conditions(self):
        """设置终止条件"""
        # 当优化完成时终止
        self.optimization_complete = TextMentionTermination("OPTIMIZATION_COMPLETE")
        
        # 当审查通过且没有需要优化的内容时终止
        self.review_approved = TextMentionTermination("REVIEW_APPROVED")
        
        # 最大消息数限制，防止无限循环
        self.max_messages = MaxMessageTermination(max_messages=20)
        
        # 组合终止条件
        self.termination_condition = (
            self.optimization_complete | 
            self.review_approved | 
            self.max_messages
        )
    
    def _setup_team(self):
        """设置团队"""
        # 按照工作流顺序：编写 -> 审查 -> 优化
        self.team = RoundRobinGroupChat(
            participants=[
                self.coder_agent,
                self.reviewer_agent, 
                self.optimizer_agent
            ],
            termination_condition=self.termination_condition
        )
    
    async def run_programming_task(self, task_description: str, show_console: bool = True):
        """
        运行编程任务
        
        Args:
            task_description: 编程任务描述
            show_console: 是否在控制台显示过程
            
        Returns:
            TaskResult: 包含所有消息的任务结果
        """
        print(f"🚀 开始编程工作流: {task_description}")
        print("=" * 60)
        
        if show_console:
            # 使用Console显示实时过程
            result = await Console(
                self.team.run_stream(task=task_description)
            )
        else:
            # 直接运行不显示过程
            result = await self.team.run(task=task_description)
        
        print("\n" + "=" * 60)
        print(f"✅ 工作流完成，原因: {result.stop_reason}")
        print(f"📊 总消息数: {len(result.messages)}")
        
        return result
    
    async def reset_workflow(self):
        """重置工作流状态"""
        await self.team.reset()
        print("🔄 工作流已重置")
    
    async def close(self):
        """关闭模型客户端连接"""
        await self.model_client.close()
        print("🔒 模型客户端连接已关闭")


async def main():
    """主函数示例"""
    # 创建工作流实例
    workflow = ProgrammingWorkflow()
    
    try:
        # 示例任务1：实现一个简单的数据结构
        task1 = """
        请实现一个Python类来表示一个简单的栈(Stack)数据结构，要求：
        1. 支持push(入栈)和pop(出栈)操作
        2. 支持peek(查看栈顶元素)操作
        3. 支持is_empty(检查是否为空)操作
        4. 支持size(获取栈大小)操作
        5. 包含适当的错误处理
        """
        
        result1 = await workflow.run_programming_task(task1)
        
        # 重置工作流准备下一个任务
        await workflow.reset_workflow()
        
        # 示例任务2：实现一个算法
        task2 = """
        请实现一个Python函数来解决以下问题：
        给定一个整数数组，找出其中两个数的和等于目标值的所有唯一组合。
        要求时间复杂度尽可能低，并处理边界情况。
        """
        
        result2 = await workflow.run_programming_task(task2)
        
    finally:
        # 关闭连接
        await workflow.close()


if __name__ == "__main__":
    # 运行示例
    asyncio.run(main())

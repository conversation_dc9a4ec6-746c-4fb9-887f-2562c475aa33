"""
AutoGen编程工作流使用示例
展示不同类型的编程任务和工作流配置
"""

import asyncio
import os
from programming_workflow import ProgrammingWorkflow


class AdvancedProgrammingWorkflow(ProgrammingWorkflow):
    """高级编程工作流，支持更多自定义配置"""
    
    def __init__(self, model_name: str = "gpt-4o", api_key: str = None, 
                 max_rounds: int = 10, enable_reflection: bool = True):
        """
        初始化高级工作流
        
        Args:
            model_name: 模型名称
            api_key: API密钥
            max_rounds: 最大轮次
            enable_reflection: 是否启用反思功能
        """
        self.max_rounds = max_rounds
        self.enable_reflection = enable_reflection
        super().__init__(model_name, api_key)
    
    def _setup_agents(self):
        """重写agent设置，添加更多专业化配置"""
        
        # 高级代码编写者 - 支持多种编程语言和框架
        self.coder_agent = AssistantAgent(
            name="senior_coder",
            model_client=self.model_client,
            system_message="""你是一个资深的软件工程师，精通多种编程语言和框架。你的职责是：

🎯 核心职责：
1. 根据需求分析，选择最适合的技术栈和设计模式
2. 编写高质量、可扩展、可维护的代码
3. 实现完整的错误处理和边界条件检查
4. 添加详细的文档字符串和注释
5. 遵循SOLID原则和最佳实践

💡 技术要求：
- 优先考虑代码的可读性和可维护性
- 使用类型提示(Type Hints)
- 实现适当的设计模式
- 考虑性能优化和内存效率
- 包含单元测试示例

📝 输出格式：
- 提供完整的代码实现
- 说明设计思路和技术选择
- 列出主要功能和使用方法
- 标注需要特别注意的地方

完成代码编写后，请说"CODE_IMPLEMENTATION_READY"。""",
            reflect_on_tool_use=self.enable_reflection,
        )
        
        # 高级代码审查者 - 多维度审查
        self.reviewer_agent = AssistantAgent(
            name="senior_reviewer",
            model_client=self.model_client,
            system_message="""你是一个经验丰富的技术架构师和代码审查专家。你需要从多个维度审查代码：

🔍 审查维度：
1. **功能正确性**: 代码是否正确实现了需求
2. **代码质量**: 可读性、可维护性、可扩展性
3. **性能效率**: 时间复杂度、空间复杂度、算法优化
4. **安全性**: 潜在的安全漏洞和风险
5. **最佳实践**: 是否遵循语言和框架的最佳实践
6. **测试覆盖**: 是否需要额外的测试用例

📊 审查报告格式：
### 代码审查报告

#### ✅ 优点
- [列出代码的优点]

#### ⚠️ 发现的问题
- [按严重程度列出问题]

#### 🔧 改进建议
- [提供具体的改进建议]

#### 📈 性能评估
- [评估性能和优化建议]

#### 🛡️ 安全评估
- [安全性分析]

#### 📝 总体评价
- [总体评价和建议]

如果代码质量优秀，无需重大修改，请在报告最后说"REVIEW_APPROVED"。
如果需要优化，请详细说明需要改进的地方。""",
        )
        
        # 高级代码优化者 - 全面优化
        self.optimizer_agent = AssistantAgent(
            name="senior_optimizer",
            model_client=self.model_client,
            system_message="""你是一个代码优化和重构专家。基于原始代码和审查意见，进行全面优化：

🚀 优化目标：
1. **性能优化**: 改进算法复杂度，优化数据结构选择
2. **代码重构**: 提升代码结构和可读性
3. **错误处理**: 完善异常处理和边界条件
4. **文档完善**: 改进注释和文档字符串
5. **测试增强**: 添加更全面的测试用例
6. **安全加固**: 修复安全漏洞

🔧 优化策略：
- 保持功能完整性的前提下进行优化
- 使用更高效的算法和数据结构
- 改进代码组织和模块化
- 增强错误处理和日志记录
- 添加配置和扩展点

📋 优化报告格式：
### 代码优化报告

#### 🔄 主要优化内容
- [列出主要优化项目]

#### ⚡ 性能提升
- [性能改进说明]

#### 🛠️ 代码改进
- [代码质量提升]

#### 🧪 测试增强
- [测试覆盖改进]

#### 📚 文档完善
- [文档改进说明]

完成优化后，请提供完整的优化代码，并说"OPTIMIZATION_COMPLETE"。""",
        )


async def run_data_structure_example():
    """运行数据结构实现示例"""
    print("🏗️ 数据结构实现示例")
    print("=" * 50)
    
    workflow = AdvancedProgrammingWorkflow(max_rounds=15)
    
    task = """
    请实现一个高效的LRU (Least Recently Used) 缓存类，要求：
    
    功能需求：
    1. 支持get(key)操作，获取缓存值
    2. 支持put(key, value)操作，设置缓存值
    3. 当缓存满时，移除最久未使用的项目
    4. 所有操作的时间复杂度应为O(1)
    
    技术需求：
    1. 使用适当的数据结构组合
    2. 包含完整的类型提示
    3. 实现线程安全版本
    4. 添加统计功能(命中率、缓存大小等)
    5. 包含完整的单元测试
    6. 支持序列化和反序列化
    """
    
    try:
        result = await workflow.run_programming_task(task)
        return result
    finally:
        await workflow.close()


async def run_algorithm_example():
    """运行算法实现示例"""
    print("🧮 算法实现示例")
    print("=" * 50)
    
    workflow = AdvancedProgrammingWorkflow(max_rounds=12)
    
    task = """
    请实现一个智能路径规划算法，解决以下问题：
    
    问题描述：
    在一个二维网格地图中，找到从起点到终点的最短路径，地图中包含：
    - 可通行区域 (0)
    - 障碍物 (1) 
    - 不同权重的地形 (2-9，数字表示通过成本)
    
    算法要求：
    1. 实现A*算法或Dijkstra算法
    2. 支持8方向移动(包括对角线)
    3. 考虑地形权重影响
    4. 返回路径和总成本
    5. 支持动态障碍物更新
    
    技术要求：
    1. 使用优先队列优化性能
    2. 实现可视化功能
    3. 支持大规模地图(1000x1000以上)
    4. 包含性能基准测试
    5. 提供多种启发式函数选择
    6. 支持路径平滑处理
    """
    
    try:
        result = await workflow.run_programming_task(task)
        return result
    finally:
        await workflow.close()


async def run_web_api_example():
    """运行Web API开发示例"""
    print("🌐 Web API开发示例")
    print("=" * 50)
    
    workflow = AdvancedProgrammingWorkflow(max_rounds=18)
    
    task = """
    请使用FastAPI框架实现一个RESTful API服务，功能需求：
    
    业务功能：
    1. 用户管理系统(注册、登录、个人信息管理)
    2. JWT身份验证和授权
    3. 数据库操作(使用SQLAlchemy ORM)
    4. 文件上传和下载
    5. 实时通知(WebSocket)
    
    技术要求：
    1. 使用Pydantic进行数据验证
    2. 实现中间件(日志、CORS、限流)
    3. 集成Redis缓存
    4. 实现API文档自动生成
    5. 添加单元测试和集成测试
    6. 支持Docker容器化部署
    7. 实现健康检查端点
    8. 添加API版本控制
    
    安全要求：
    1. 输入验证和SQL注入防护
    2. 密码加密存储
    3. API访问频率限制
    4. 敏感信息脱敏
    """
    
    try:
        result = await workflow.run_programming_task(task)
        return result
    finally:
        await workflow.close()


async def main():
    """主函数 - 运行所有示例"""
    print("🤖 AutoGen编程工作流示例集合")
    print("=" * 60)
    
    # 检查API密钥
    if not os.getenv("OPENAI_API_KEY"):
        print("⚠️ 警告: 未设置OPENAI_API_KEY环境变量")
        print("请设置环境变量或在代码中提供API密钥")
        return
    
    examples = [
        ("数据结构实现", run_data_structure_example),
        ("算法实现", run_algorithm_example), 
        ("Web API开发", run_web_api_example),
    ]
    
    for name, example_func in examples:
        print(f"\n🎯 开始运行: {name}")
        try:
            await example_func()
            print(f"✅ {name} 完成")
        except Exception as e:
            print(f"❌ {name} 失败: {e}")
        
        print("\n" + "="*60)


if __name__ == "__main__":
    asyncio.run(main())

#!/usr/bin/env python3
"""
AutoGen编程工作流测试脚本
验证工作流的基本功能和配置
"""

import asyncio
import os
import sys
from unittest.mock import Mock, AsyncMock
import pytest

# 添加当前目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from programming_workflow import ProgrammingWorkflow
from workflow_examples import AdvancedProgrammingWorkflow
from config import config, check_environment, get_task_template


class TestWorkflow:
    """工作流测试类"""
    
    @pytest.mark.asyncio
    async def test_basic_workflow_creation(self):
        """测试基础工作流创建"""
        # 模拟API密钥
        os.environ["OPENAI_API_KEY"] = "test-key"
        
        workflow = ProgrammingWorkflow(api_key="test-key")
        
        # 验证agent创建
        assert workflow.coder_agent is not None
        assert workflow.reviewer_agent is not None
        assert workflow.optimizer_agent is not None
        assert workflow.team is not None
        
        await workflow.close()
    
    @pytest.mark.asyncio
    async def test_advanced_workflow_creation(self):
        """测试高级工作流创建"""
        os.environ["OPENAI_API_KEY"] = "test-key"
        
        workflow = AdvancedProgrammingWorkflow(
            api_key="test-key",
            max_rounds=10,
            enable_reflection=True
        )
        
        assert workflow.max_rounds == 10
        assert workflow.enable_reflection == True
        
        await workflow.close()
    
    def test_config_loading(self):
        """测试配置加载"""
        assert len(config.model_configs) > 0
        assert len(config.agent_configs) > 0
        assert config.workflow_config is not None
        
        # 测试获取配置
        model_config = config.get_model_config("gpt-4o")
        assert model_config.model_name == "gpt-4o"
        
        agent_config = config.get_agent_config("coder")
        assert agent_config.name == "coder"
    
    def test_task_templates(self):
        """测试任务模板"""
        # 测试数据结构模板
        task = get_task_template("data_structure", data_structure_name="栈")
        assert "栈" in task
        assert "数据结构" in task
        
        # 测试算法模板
        task = get_task_template("algorithm", 
                               algorithm_name="快速排序",
                               problem_description="对数组进行排序")
        assert "快速排序" in task
        assert "排序" in task
    
    def test_environment_check(self):
        """测试环境检查"""
        # 保存原始环境变量
        original_key = os.environ.get("OPENAI_API_KEY")
        
        try:
            # 测试缺少API密钥的情况
            if "OPENAI_API_KEY" in os.environ:
                del os.environ["OPENAI_API_KEY"]
            assert check_environment() == False
            
            # 测试有API密钥的情况
            os.environ["OPENAI_API_KEY"] = "test-key"
            assert check_environment() == True
            
        finally:
            # 恢复原始环境变量
            if original_key:
                os.environ["OPENAI_API_KEY"] = original_key
            elif "OPENAI_API_KEY" in os.environ:
                del os.environ["OPENAI_API_KEY"]


async def test_mock_workflow():
    """使用模拟对象测试工作流"""
    print("🧪 运行模拟工作流测试")
    
    # 创建模拟的模型客户端
    mock_client = Mock()
    mock_client.close = AsyncMock()
    
    # 模拟工作流
    workflow = ProgrammingWorkflow(api_key="test-key")
    workflow.model_client = mock_client
    
    # 模拟团队运行结果
    from autogen_agentchat.base import TaskResult
    from autogen_agentchat.messages import TextMessage
    
    mock_result = TaskResult(
        messages=[
            TextMessage(source="user", content="测试任务", models_usage=None),
            TextMessage(source="coder", content="代码实现", models_usage=None),
            TextMessage(source="reviewer", content="代码审查", models_usage=None),
            TextMessage(source="optimizer", content="OPTIMIZATION_COMPLETE", models_usage=None),
        ],
        stop_reason="OPTIMIZATION_COMPLETE"
    )
    
    # 模拟团队运行方法
    workflow.team.run = AsyncMock(return_value=mock_result)
    
    # 测试运行
    result = await workflow.team.run(task="测试任务")
    
    assert result.stop_reason == "OPTIMIZATION_COMPLETE"
    assert len(result.messages) == 4
    
    await workflow.close()
    print("✅ 模拟工作流测试通过")


async def test_simple_task():
    """测试简单任务处理"""
    print("📝 测试简单任务处理")
    
    # 检查是否有真实的API密钥
    if not os.getenv("OPENAI_API_KEY") or os.getenv("OPENAI_API_KEY") == "test-key":
        print("⚠️ 跳过真实API测试 - 未设置有效的OPENAI_API_KEY")
        return
    
    try:
        workflow = ProgrammingWorkflow()
        
        # 简单的编程任务
        task = """
        请实现一个简单的Python函数，计算两个数的最大公约数(GCD)。
        要求：
        1. 使用欧几里得算法
        2. 包含输入验证
        3. 添加文档字符串
        """
        
        print(f"🎯 任务: {task[:50]}...")
        
        # 设置较短的超时和轮次限制
        workflow.termination_condition = workflow.max_messages  # 只使用消息数限制
        
        result = await workflow.run_programming_task(task, show_console=False)
        
        print(f"✅ 任务完成: {result.stop_reason}")
        print(f"📊 消息数量: {len(result.messages)}")
        
        # 验证结果包含预期内容
        all_content = " ".join([msg.content for msg in result.messages if hasattr(msg, 'content')])
        assert "gcd" in all_content.lower() or "最大公约数" in all_content
        
        await workflow.close()
        print("✅ 简单任务测试通过")
        
    except Exception as e:
        print(f"❌ 简单任务测试失败: {e}")


async def run_all_tests():
    """运行所有测试"""
    print("🚀 开始运行AutoGen工作流测试")
    print("=" * 60)
    
    # 运行模拟测试
    await test_mock_workflow()
    
    # 运行简单任务测试
    await test_simple_task()
    
    # 运行单元测试
    print("\n🔬 运行单元测试")
    test_instance = TestWorkflow()
    
    try:
        test_instance.test_config_loading()
        print("✅ 配置加载测试通过")
        
        test_instance.test_task_templates()
        print("✅ 任务模板测试通过")
        
        test_instance.test_environment_check()
        print("✅ 环境检查测试通过")
        
    except Exception as e:
        print(f"❌ 单元测试失败: {e}")
    
    print("\n" + "=" * 60)
    print("🎉 测试完成")


def main():
    """主函数"""
    import argparse
    
    parser = argparse.ArgumentParser(description="AutoGen工作流测试")
    parser.add_argument("--unit-only", action="store_true", help="只运行单元测试")
    parser.add_argument("--integration-only", action="store_true", help="只运行集成测试")
    
    args = parser.parse_args()
    
    if args.unit_only:
        # 只运行单元测试
        test_instance = TestWorkflow()
        test_instance.test_config_loading()
        test_instance.test_task_templates()
        test_instance.test_environment_check()
        print("✅ 单元测试完成")
    elif args.integration_only:
        # 只运行集成测试
        asyncio.run(test_simple_task())
    else:
        # 运行所有测试
        asyncio.run(run_all_tests())


if __name__ == "__main__":
    main()
